#!/usr/bin/env python3
"""Demo script to test improved NRT parsing."""

import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.parsers.niche_markup_parser import NicheMarkupPars<PERSON>


def test_nrt_parsing():
    """Test NRT parsing with the actual file."""
    
    # Read the actual NRT file
    nrt_file = r'c:\Users\<USER>\Desktop\1.nrt'
    
    try:
        with open(nrt_file, 'r', encoding='utf-8') as f:
            content = f.read()
    except FileNotFoundError:
        print(f"NRT file not found: {nrt_file}")
        return
    
    print("=" * 60)
    print("NRT PARSER IMPROVEMENT DEMO")
    print("=" * 60)
    
    # Initialize parser
    parser = NicheMarkupParser()
    metadata = {'category': 'niche_markup', 'real_type': 'nrt'}
    
    print(f"Original file size: {len(content)} characters")
    print(f"Original content preview (first 200 chars):")
    print(content[:200] + "...")
    print()
    
    # Parse the content
    try:
        result = parser.parse(content.encode('utf-8'), metadata)
        
        if result['parsing_successful']:
            text = result['normalized_text']
            
            print("✅ PARSING SUCCESSFUL!")
            print(f"Extracted text length: {len(text)} characters")
            print(f"Compression ratio: {len(content)/len(text):.1f}:1")
            print()
            
            print("📋 EXTRACTED CONTENT:")
            print("-" * 40)
            
            # Split into lines and show key information
            lines = [line.strip() for line in text.split('\n') if line.strip()]
            
            # Show section headers
            sections = [line for line in lines if line.startswith('===')]
            print("📁 SECTIONS FOUND:")
            for section in sections:
                print(f"  {section}")
            print()
            
            # Show key-value pairs
            key_values = [line for line in lines if ':' in line and not line.startswith('===')]
            print("🔑 KEY INFORMATION EXTRACTED:")
            for kv in key_values[:10]:  # Show first 10
                print(f"  {kv}")
            if len(key_values) > 10:
                print(f"  ... and {len(key_values) - 10} more")
            print()
            
            # Check for artifacts
            artifacts = []
            if '{' in text or '}' in text:
                artifacts.append("Curly braces found")
            if 'nicherms:' in text:
                artifacts.append("Navigation commands found")
            if 'CLICK HERE' in text:
                artifacts.append("UI instructions found")
            
            if artifacts:
                print("⚠️  REMAINING ARTIFACTS:")
                for artifact in artifacts:
                    print(f"  - {artifact}")
            else:
                print("✅ NO MARKUP ARTIFACTS REMAINING!")
            
            print()
            print("📄 FULL EXTRACTED TEXT:")
            print("-" * 40)
            print(text)
            
        else:
            print("❌ PARSING FAILED!")
            print(f"Error: {result.get('error', 'Unknown error')}")
            
    except Exception as e:
        print(f"❌ EXCEPTION DURING PARSING: {e}")


if __name__ == "__main__":
    test_nrt_parsing()
