"""Tests for NRT parser improvements."""

import pytest
from src.parsers.niche_markup_parser import NicheMarkupParser


class TestNRTParser:
    """Test cases for improved NRT parsing."""
    
    def setup_method(self):
        """Setup test fixtures."""
        self.parser = NicheMarkupParser()
        
        # Sample NRT content based on the actual file structure
        self.sample_nrt = """{ntf1{p1l{b GENERAL OCCURRENCE REPORT}}{pal}{p2l{b Occurrence Date and Time}}{t{tc 144}{tc}{tr{c{pal Start Date/Time}}{c{pal{lnk{nav nicherms:InsertDateTime:GenDateTimeTZV2G}2025/05/29 13:39}}}}{tr{c{pal End Date/Time (optional)}}{c{pal{lnk{nav nicherms:InsertDateTime:GenDateTimeTZV2G}Date/time}}}}}{pal}{p2l{b Occurrence Address}}{t{tc 144}{tc}{tr{c{pal Address}}{c{pal 3495 North Service Road, Burlington ON}}}{tr{c{pal Address Type}}{c{pal{lnk{nav nicherms:ReplaceTextWithNMLChoices:cl_ExtendedUCRIncidentLocation:ShowBlankOption:1}Parking lots}}}}}{pal}{p2l{b Person Details}}{t{tc 150}{tc}{tr{c{pil Involvement}}{c{pal{lnk{nav nicherms:ReplaceTextWithNMLChoices:cl_GOccIvGPersonClassification}Complainant}}}}{tr{c{pil Name}}{c{pal Nirabkumar BAROT}}}{tr{c{pil DOB}}{c}}{tr{c{pil Gender}}{c{pal{lnk{nav nicherms:ReplaceTextWithNMLChoices:cl_GPersonNameGender}CLICK HERE and either scroll and select OR start typing}}}}{tr{c{pil Race}}{c{pal{lnk{nav nicherms:ReplaceTextWithNMLChoices:cl_descRace}CLICK HERE and either scroll and select OR start typing}}}}{tr{c{pil Address}}{c{pal 11 Rosewell Crescent, Georgetown ON}}}{tr{c{pil Phone #}}{c{pal ************}}}{tr{c{pil Email, Social Media, etc.}}{c}}{tr{c{pil Additional Information}}{c}}}{pal{lnk{nav nicherms:InsertRichText:Preserve:RichTextSourceEntity:GOccReport:RichTextItemKeys:INVOLVED_PERSON}+ Add Person Information}}{pal}{pal{lnk{nav nicherms:InsertRichText:Preserve:RichTextSourceEntity:GOccReport:RichTextItemKeys:VEHICLE}+ Add Vehicle Information}}{pal}{p1l{b Narrative}}{t{tc}{tr{c{pal 2025-JUN-3 }{pal 09:46 hrs }{pal}{pal The Complainant:}{pal BAROT Nirabkumar}{pal 11 Rosewell Crescent}{pal Georgetown ON}{pal C: ************}{pal}{pal The Suspect:}{pal Unknown}{pal}{pal The Suspect Vehicle:}{pal Ontario Licence Plate: GVFH605, white Tesla}{pal}{pal Location of Incident:}{pal Cash and Carry}{pal 3495 North Service Road}{pal Burlington ON}{pal}{pal Time of Incident:}{pal May 29 2025}{pal}{pal On May 29 2025, the Complainant contacted HRPS regards to a property damage under incident.}{pal}{pal On the above date, the Complainant was at Cash and Carry located at 3495 North Service Road in the City of Burlington for shopping. His vehicle was damaged to a rear bumper.}{pal}{pal On June 3 2025, police called and left a voicemail and requested for further information. As of June 17th 2025 the complainant has still not provided the necessary information. If the complainant does respond with the necessary information, the investigation will be re-opened.  }{pal}{pal Cleared Unfounded.  }{pal  }{pal}}}}{pal}{p2l{b UCR}}{pal{rt UNFOUNDED}}{t{tc 37}{tc}{tr{c{pal UCR:}}{c{pal  {lnk{nav nicherms:PlaceholderText:Remove}Type UCR here (eg Assault, CC 266 Assault)}}}}}{pal}{pal}{p2l{b Additional Templates (if applicable)}}{pal{lnk{nav nicherms:InsertRichText:Remove:RichTextSourceEntity:GOccReport:RichTextItemKeys:ADDITIONALTEMPLATE/SEIZEDPROPERTYREPORT}+ Select Template}}{pal}}"""
    
    def test_parse_nrt_structure(self):
        """Test parsing of NRT structure."""
        metadata = {'category': 'niche_markup', 'real_type': 'nrt'}
        
        result = self.parser.parse(self.sample_nrt.encode('utf-8'), metadata)
        
        assert result['parsing_successful'] is True
        text = result['normalized_text']
        
        # Check that section headers are properly extracted
        assert "GENERAL OCCURRENCE REPORT" in text
        assert "Occurrence Date and Time" in text
        assert "Occurrence Address" in text
        assert "Person Details" in text
        assert "Narrative" in text
        
        # Check that key data is extracted
        assert "2025/05/29 13:39" in text
        assert "3495 North Service Road, Burlington ON" in text
        assert "Nirabkumar BAROT" in text
        assert "************" in text
        assert "11 Rosewell Crescent, Georgetown ON" in text
        
        # Check that narrative content is preserved
        assert "The Complainant contacted HRPS" in text
        assert "property damage under incident" in text
        assert "Cash and Carry" in text
        assert "white Tesla" in text
        assert "GVFH605" in text
        
    def test_nrt_section_headers(self):
        """Test section header processing."""
        test_content = "{p1l{b GENERAL OCCURRENCE REPORT}}{p2l{b Person Details}}"
        
        result = self.parser._parse_nrt_structure(test_content)
        
        assert "=== GENERAL OCCURRENCE REPORT ===" in result
        assert "=== PERSON DETAILS ===" in result
    
    def test_nrt_table_processing(self):
        """Test table row processing."""
        test_content = "{t{tc 144}{tc}{tr{c{pal Address}}{c{pal 123 Main Street}}}{tr{c{pal Phone}}{c{pal 555-1234}}}}"
        
        result = self.parser._parse_nrt_structure(test_content)
        
        assert "Address: 123 Main Street" in result
        assert "Phone: 555-1234" in result
    
    def test_nrt_link_removal(self):
        """Test removal of navigation links."""
        test_content = "{lnk{nav nicherms:InsertDateTime:GenDateTimeTZV2G}2025/05/29 13:39}"
        
        result = self.parser._process_nrt_links(test_content)
        
        assert "2025/05/29 13:39" in result
        assert "nicherms:" not in result
    
    def test_nrt_ui_instruction_removal(self):
        """Test removal of UI instructions."""
        test_content = "{lnk{nav nicherms:ReplaceTextWithNMLChoices:cl_GPersonNameGender}CLICK HERE and either scroll and select OR start typing}"
        
        result = self.parser._process_nrt_links(test_content)
        
        # UI instructions should be removed
        assert "CLICK HERE and either scroll and select OR start typing" not in result
        assert result.strip() == ""
    
    def test_nrt_paragraph_processing(self):
        """Test paragraph marker processing."""
        test_content = "Line 1{pal}Line 2{pal}Line 3"
        
        result = self.parser._process_nrt_paragraphs(test_content)
        
        assert "Line 1\nLine 2\nLine 3" in result
    
    def test_nrt_formatting_removal(self):
        """Test formatting tag removal."""
        test_content = "{b Bold Text}{rt Rich Text}"
        
        result = self.parser._process_nrt_formatting(test_content)
        
        assert "Bold Text" in result
        assert "Rich Text" in result
        assert "{b" not in result
        assert "{rt" not in result
    
    def test_nrt_cleanup_artifacts(self):
        """Test cleanup of remaining artifacts."""
        test_content = "Good text {unknown_tag} more text\n\n\n\nExtra spaces   here"
        
        result = self.parser._cleanup_nrt_artifacts(test_content)
        
        assert "Good text  more text" in result
        assert "{unknown_tag}" not in result
        assert "\n\n\n\n" not in result
        assert "   " not in result
    
    def test_full_nrt_parsing_integration(self):
        """Test full integration of NRT parsing."""
        metadata = {'category': 'niche_markup', 'real_type': 'nrt'}
        
        result = self.parser.parse(self.sample_nrt.encode('utf-8'), metadata)
        
        assert result['parsing_successful'] is True
        text = result['normalized_text']
        
        # Verify no markup artifacts remain
        assert "{" not in text
        assert "}" not in text
        assert "nicherms:" not in text
        assert "CLICK HERE and either scroll" not in text
        
        # Verify meaningful content is preserved and structured
        lines = [line.strip() for line in text.split('\n') if line.strip()]
        
        # Should have section headers
        section_headers = [line for line in lines if line.startswith('===')]
        assert len(section_headers) >= 4  # At least 4 main sections
        
        # Should have key-value pairs
        key_value_pairs = [line for line in lines if ':' in line and not line.startswith('===')]
        assert len(key_value_pairs) >= 5  # Should have several key-value pairs
        
        # Should preserve important details
        assert any("Nirabkumar BAROT" in line for line in lines)
        assert any("3495 North Service Road" in line for line in lines)
        assert any("************" in line for line in lines)
