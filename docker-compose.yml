version: '3.8'

services:
  niche-etl:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: niche-text-etl
    restart: unless-stopped
    
    environment:
      - CONFIG_PATH=/app/config.yaml
      - LOG_LEVEL=INFO
    
    env_file:
      - .env
    
    volumes:
      - ./logs:/app/logs
      - ./config.yaml:/app/config.yaml:ro
      - ./data:/app/data  # Optional: for local data storage
    
    networks:
      - etl-network
    
    # Resource limits
    deploy:
      resources:
        limits:
          memory: 4G
          cpus: '2.0'
        reservations:
          memory: 1G
          cpus: '0.5'
    
    # Health check
    healthcheck:
      test: ["CMD", "python", "-c", "import sys; sys.exit(0)"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

networks:
  etl-network:
    driver: bridge
