"""Tests for decompression module."""

import pytest
import gzip
import io
from src.decompression import Decompressor, DecompressionError


class TestDecompressor:
    """Test cases for Decompressor class."""
    
    def setup_method(self):
        """Setup test fixtures."""
        self.decompressor = Decompressor(max_size_mb=1)
        self.test_data = b"This is test data for compression testing."
    
    def test_decompress_raw_data(self):
        """Test decompression of raw (uncompressed) data."""
        data, metadata = self.decompressor.decompress(self.test_data, is_gzipped=False)
        
        assert data == self.test_data
        assert metadata['is_compressed'] is False
        assert metadata['decompression_successful'] is True
        assert metadata['original_size'] == len(self.test_data)
        assert metadata['decompressed_size'] == len(self.test_data)
        assert metadata['compression_ratio'] == 1.0
    
    def test_decompress_gzipped_data(self):
        """Test decompression of gzipped data."""
        # Create gzipped test data
        compressed_stream = io.BytesIO()
        with gzip.GzipFile(fileobj=compressed_stream, mode='wb') as gz_file:
            gz_file.write(self.test_data)
        compressed_data = compressed_stream.getvalue()
        
        # Decompress
        data, metadata = self.decompressor.decompress(compressed_data, is_gzipped=True)
        
        assert data == self.test_data
        assert metadata['is_compressed'] is True
        assert metadata['decompression_successful'] is True
        assert metadata['original_size'] == len(compressed_data)
        assert metadata['decompressed_size'] == len(self.test_data)
        assert metadata['compression_ratio'] > 0
    
    def test_decompress_invalid_gzip(self):
        """Test decompression with invalid gzip data."""
        invalid_gzip_data = b"This is not gzipped data"
        
        with pytest.raises(DecompressionError):
            self.decompressor.decompress(invalid_gzip_data, is_gzipped=True)
    
    def test_decompress_size_limit_exceeded(self):
        """Test decompression with size limit exceeded."""
        # Create large test data (larger than 1MB limit)
        large_data = b"x" * (2 * 1024 * 1024)  # 2MB
        
        with pytest.raises(DecompressionError) as exc_info:
            self.decompressor.decompress(large_data, is_gzipped=False)
        
        assert "exceeds limit" in str(exc_info.value)
    
    def test_is_likely_compressed(self):
        """Test gzip magic number detection."""
        # Create gzipped data
        compressed_stream = io.BytesIO()
        with gzip.GzipFile(fileobj=compressed_stream, mode='wb') as gz_file:
            gz_file.write(self.test_data)
        compressed_data = compressed_stream.getvalue()
        
        assert self.decompressor.is_likely_compressed(compressed_data) is True
        assert self.decompressor.is_likely_compressed(self.test_data) is False
        assert self.decompressor.is_likely_compressed(b"") is False
        assert self.decompressor.is_likely_compressed(b"x") is False
    
    def test_validate_decompressed_content(self):
        """Test content validation."""
        # Test with valid UTF-8 text
        validation = self.decompressor.validate_decompressed_content(self.test_data)
        assert validation['is_valid'] is True
        assert validation['encoding_detected'] == 'utf-8'
        
        # Test with empty data
        validation = self.decompressor.validate_decompressed_content(b"")
        assert validation['is_valid'] is False
        assert "Empty data" in validation['warnings'][0]
        
        # Test with PDF-like data
        pdf_data = b"%PDF-1.4\n%some pdf content"
        validation = self.decompressor.validate_decompressed_content(pdf_data)
        assert 'pdf' in validation['content_type_hints']
        
        # Test with HTML-like data
        html_data = b"<html><body>Test</body></html>"
        validation = self.decompressor.validate_decompressed_content(html_data)
        assert 'xml_or_html' in validation['content_type_hints']
