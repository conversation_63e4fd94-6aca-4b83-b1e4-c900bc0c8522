# Niche Text ETL

A comprehensive data migration and normalization pipeline for extracting text content from SQL Server blob data, with support for multiple document formats and automated monitoring.

## Overview

This ETL pipeline extracts binary data from a source SQL Server database, decompresses it if needed, parses various document formats to extract plain text, and loads the normalized results into a destination database.

### Supported Document Types

- **Microsoft Word** (.doc, .docx) - using `python-docx` and `textract`
- **Microsoft Excel** (.xls, .xlsx) - using `openpyxl` and `textract`
- **PDF** documents - using `pdfminer.six` and `PyPDF2`
- **HTML/XML/JSON/YAML** markup - using `BeautifulSoup` and `html2text`
- **Niche-specific markup** (.nrt, .nxdx) - custom parsers
- **Plain text** (.txt) - direct text extraction

## Features

- **Incremental Processing**: Resumes from last processed record
- **Multi-threaded**: Parallel processing for improved performance
- **Robust Error Handling**: Continues processing despite individual record failures
- **Comprehensive Logging**: Detailed logs with configurable retention
- **Email Alerts**: Daily monitoring reports with error summaries
- **Docker Support**: Containerized deployment option
- **Modular Design**: Easy to extend with new parsers

## Quick Start

### 1. Installation

```bash
# Clone the repository
git clone <repository-url>
cd niche_text_etl

# Install dependencies
pip install -r requirements.txt
```

### 2. Database Setup

Run the SQL script to create destination tables:

```sql
-- On your destination SQL Server database
sqlcmd -S your-server -d your-database -i sql/create_destination_tables.sql
```

### 3. Configuration

```bash
# Copy and edit configuration
cp config.yaml.example config.yaml
cp .env.example .env

# Edit config.yaml with your database settings
# Edit .env with your credentials
```

### 4. Validate Setup

```bash
python main.py validate
```

### 5. Run ETL Pipeline

```bash
python main.py run
```

## Configuration

The system uses a YAML configuration file (`config.yaml`) with the following sections:

### Database Configuration
```yaml
database:
  source:
    server: "source-server.domain.com"
    database: "source_database"
    # ... other settings
  destination:
    server: "dest-server.domain.com"
    database: "dest_database"
    # ... other settings
```

### Processing Configuration
```yaml
processing:
  batch_size: 1000
  max_workers: 4
  categories_to_process:
    - "ms_word"
    - "ms_excel"
    - "pdf"
    - "markup"
    - "niche_markup"
    - "text"
```

### Email Alerts
```yaml
email:
  smtp_server: "smtp.company.com"
  sender: "<EMAIL>"
  recipients:
    - "<EMAIL>"
  alert_schedule: "08:00"
```

## Usage

### Command Line Interface

```bash
# Run the ETL pipeline
python main.py run

# Validate configuration
python main.py validate

# Run daily monitoring
python main.py monitor

# Analyze recent logs
python main.py analyze-logs --hours 24

# Show system information
python main.py info

# Clean up old logs
python main.py cleanup
```

### Docker Deployment

```bash
# Build image
docker build -t niche-text-etl .

# Run with docker-compose
docker-compose up -d

# View logs
docker-compose logs -f
```

## Architecture

### Core Components

1. **DbClient** - Database connection and batch processing
2. **Decompressor** - Handles gzip decompression
3. **Parser Framework** - Modular content parsers
4. **Pipeline** - Main orchestration with threading
5. **Monitor** - Log analysis and alerting

### Data Flow

```
Source DB → Fetch Batch → Decompress → Parse → Normalize → Destination DB
                ↓
            Update Checkpoint
```

### Threading Model

- Main thread handles database I/O and coordination
- Worker threads process individual records in parallel
- Thread-safe statistics tracking
- Configurable worker pool size

## Monitoring

### Daily Monitoring Tasks

- Analyze logs for errors and warnings
- Send email alerts if error threshold exceeded
- Rotate and clean up old log files
- Generate summary statistics

### Log Analysis

The system automatically categorizes errors by type:
- Database connection issues
- Parsing failures
- Decompression errors
- Memory/size limitations

### Email Alerts

Configurable daily email reports include:
- Error and warning counts
- Recent error messages
- Error categorization
- Processing statistics

## Extending the System

### Adding New Parsers

1. Create a new parser class inheriting from `BaseParser`
2. Implement the `parse()` method and `supported_types` property
3. Register the parser in `ParserFactory`

Example:
```python
class CustomParser(BaseParser):
    @property
    def supported_types(self) -> list:
        return ['custom_format']
    
    def parse(self, data: bytes, metadata: Dict[str, Any]) -> Dict[str, Any]:
        # Implementation here
        pass
```

### Custom Content Categories

Update the SQL query in `database.py` to add new content type mappings:

```sql
CASE
  WHEN real_type LIKE 'new_type%' THEN 'new_category'
  -- ... existing mappings
END AS category
```

## Performance Tuning

### Batch Size
- Larger batches: Better database efficiency, higher memory usage
- Smaller batches: Lower memory usage, more database round trips
- Recommended: 500-2000 records per batch

### Worker Threads
- More workers: Higher CPU utilization, potential resource contention
- Fewer workers: Lower resource usage, slower processing
- Recommended: 2-8 workers depending on server capacity

### Memory Management
- Configure `memory_limit_mb` to prevent large file processing
- Monitor decompressed file sizes
- Consider file size limits in parsers

## Troubleshooting

### Common Issues

1. **Database Connection Failures**
   - Check connection strings and credentials
   - Verify network connectivity
   - Ensure SQL Server drivers are installed

2. **Parsing Errors**
   - Check if required libraries are installed
   - Verify file format detection logic
   - Review parser-specific error messages

3. **Memory Issues**
   - Reduce batch size
   - Lower memory limits
   - Check for memory leaks in parsers

4. **Performance Issues**
   - Adjust worker thread count
   - Optimize database queries
   - Consider indexing on source tables

### Log Analysis

```bash
# Check recent errors
python main.py analyze-logs --hours 24

# View detailed logs
tail -f logs/niche_etl.log

# Search for specific errors
grep "ERROR" logs/niche_etl.log | tail -20
```

## Security Considerations

- Store database credentials in environment variables
- Use SQL Server integrated authentication when possible
- Implement proper access controls on destination database
- Secure SMTP credentials for email alerts
- Regular security updates for dependencies

## License

[Your License Here]

## Support

For issues and questions:
- Check the troubleshooting section
- Review log files for detailed error information
- Contact the development team
