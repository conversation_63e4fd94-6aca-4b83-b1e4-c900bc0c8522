# Database Configuration
database:
  source:
    driver: "ODBC Driver 17 for SQL Server"
    server: "source-server.domain.com"
    database: "source_database"
    username: "${DB_SOURCE_USER}"
    password: "${DB_SOURCE_PASSWORD}"
    trusted_connection: true
    connection_timeout: 30
    command_timeout: 300
  
  destination:
    driver: "ODBC Driver 17 for SQL Server"
    server: "dest-server.domain.com"
    database: "dest_database"
    username: "${DB_DEST_USER}"
    password: "${DB_DEST_PASSWORD}"
    trusted_connection: true
    connection_timeout: 30
    command_timeout: 300

# Processing Configuration
processing:
  batch_size: 1000
  max_workers: 4
  max_retries: 3
  retry_delay: 5  # seconds
  memory_limit_mb: 2048

  # Debug/Testing Configuration
  max_records_to_process: null  # Set to a number (e.g., 100, 200) to limit processing for testing/debug
                                # Set to null or 0 for unlimited processing (production mode)
  
  # Content type categories to process
  categories_to_process:
    - "ms_word"
    - "ms_excel" 
    - "pdf"
    - "markup"
    - "niche_markup"
    - "text"
  
  # Categories to ignore
  categories_to_ignore:
    - "other"
    - "image"
    - "video"
    - "audio"

# Logging Configuration
logging:
  level: "INFO"
  log_dir: "logs"
  log_file: "niche_etl.log"
  max_file_size_mb: 100
  backup_count: 10
  retention_days: 30
  
  # Log format
  format: "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}"

# Email Configuration
email:
  smtp_server: "smtp.haltonpolice.ca"
  smtp_port: 587
  use_tls: true
  sender: "<EMAIL>"
  recipients:
    - "<EMAIL>"
  
  # Alert settings
  alert_schedule: "08:00"  # Daily at 8 AM
  alert_timezone: "America/Toronto"
  
  # Email templates
  subject_template: "Niche ETL Daily Report - {date}"
  error_threshold: 1  # Send alert if >= 1 error in last 24h

# Pipeline Configuration
pipeline:
  incremental_mode: true
  checkpoint_frequency: 100  # Save progress every N batches
  
  # Table names
  source_table: "TBL_BlobData"
  destination_table: "ProcessedDocuments"
  checkpoint_table: "ETL_Checkpoints"
  
  # SQL query parameters
  host_id_prefixes:
    - "91423001"
    - "91623001"

# Docker Configuration (optional)
docker:
  image_name: "niche-text-etl"
  tag: "latest"
  base_image: "python:3.11-slim"
  
# Monitoring
monitoring:
  health_check_interval: 300  # seconds
  metrics_retention_days: 7
  performance_log_frequency: 1000  # Log performance every N records
