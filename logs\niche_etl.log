2025-06-17 09:02:47 | INFO | src.logging_setup:setup_logging:46 | Logging initialized - Level: INFO, File: logs\niche_etl.log
2025-06-17 09:02:47 | INFO | __main__:cli:39 | Niche Text ETL starting with config: config.yaml
2025-06-17 09:02:47 | INFO | src.pipeline:__init__:108 | Pipeline initialized successfully
2025-06-17 09:02:47 | INFO | src.pipeline:_test_connections:175 | Testing database connections...
2025-06-17 09:03:02 | ERROR | src.database:get_connection:75 | Database connection error: ('08001', '[08001] [Microsoft][ODBC Driver 17 for SQL Server]Named Pipes Provider: Could not open a connection to SQL Server [53].  (53) (SQLDriverConnect); [08001] [Microsoft][ODBC Driver 17 for SQL Server]Login timeout expired (0); [08001] [Microsoft][ODBC Driver 17 for SQL Server]Invalid connection string attribute (0); [08001] [Microsoft][ODBC Driver 17 for SQL Server]A network-related or instance-specific error has occurred while establishing a connection to SQL Server. Server is not found or not accessible. Check if instance name is correct and if SQL Server is configured to allow remote connections. For more information see SQL Server Books Online. (53)')
2025-06-17 09:03:02 | ERROR | src.database:test_connection:93 | Connection test failed: ('08001', '[08001] [Microsoft][ODBC Driver 17 for SQL Server]Named Pipes Provider: Could not open a connection to SQL Server [53].  (53) (SQLDriverConnect); [08001] [Microsoft][ODBC Driver 17 for SQL Server]Login timeout expired (0); [08001] [Microsoft][ODBC Driver 17 for SQL Server]Invalid connection string attribute (0); [08001] [Microsoft][ODBC Driver 17 for SQL Server]A network-related or instance-specific error has occurred while establishing a connection to SQL Server. Server is not found or not accessible. Check if instance name is correct and if SQL Server is configured to allow remote connections. For more information see SQL Server Books Online. (53)')
2025-06-17 09:09:14 | INFO | src.logging_setup:setup_logging:46 | Logging initialized - Level: INFO, File: logs\niche_etl.log
2025-06-17 09:09:14 | INFO | __main__:cli:37 | Niche Text ETL starting with config: config.yaml
2025-06-17 09:10:01 | INFO | src.logging_setup:setup_logging:46 | Logging initialized - Level: INFO, File: logs\niche_etl.log
2025-06-17 09:10:01 | INFO | __main__:cli:37 | Niche Text ETL starting with config: config.yaml
2025-06-17 09:10:34 | INFO | src.logging_setup:setup_logging:46 | Logging initialized - Level: INFO, File: logs\niche_etl.log
2025-06-17 09:10:34 | INFO | __main__:cli:37 | Niche Text ETL starting with config: config.yaml
2025-06-17 09:11:00 | INFO | src.logging_setup:setup_logging:46 | Logging initialized - Level: INFO, File: logs\niche_etl.log
2025-06-17 09:11:00 | INFO | __main__:cli:37 | Niche Text ETL starting with config: config.yaml
