"""Logging setup for Niche Text ETL."""

import os
import sys
from pathlib import Path
from loguru import logger

from .config import LoggingConfig


def setup_logging(config: LoggingConfig) -> None:
    """Setup logging configuration.
    
    Args:
        config: Logging configuration
    """
    # Remove default logger
    logger.remove()
    
    # Create log directory if it doesn't exist
    log_dir = Path(config.log_dir)
    log_dir.mkdir(parents=True, exist_ok=True)
    
    # Console logging
    logger.add(
        sys.stdout,
        level=config.level,
        format=config.format,
        colorize=True,
        backtrace=True,
        diagnose=True
    )
    
    # File logging with rotation
    log_file_path = log_dir / config.log_file
    logger.add(
        str(log_file_path),
        level=config.level,
        format=config.format,
        rotation=f"{config.max_file_size_mb} MB",
        retention=f"{config.backup_count} files",
        compression="gz",
        backtrace=True,
        diagnose=True
    )
    
    logger.info(f"Logging initialized - Level: {config.level}, File: {log_file_path}")


def get_logger(name: str = None):
    """Get a logger instance.
    
    Args:
        name: Logger name (optional)
        
    Returns:
        Logger instance
    """
    if name:
        return logger.bind(name=name)
    return logger
