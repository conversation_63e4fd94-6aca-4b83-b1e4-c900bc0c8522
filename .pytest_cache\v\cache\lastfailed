{"tests/test_parsers.py::TestTextParser": true, "tests/test_parsers.py::TestMarkupParser": true, "tests/test_parsers.py::TestParserFactory": true, "tests/test_nrt_parser.py::TestNRTParser::test_parse_nrt_structure": true, "tests/test_nrt_parser.py::TestNRTParser::test_nrt_table_processing": true, "tests/test_nrt_parser.py::TestNRTParser::test_nrt_cleanup_artifacts": true, "tests/test_nrt_parser.py::TestNRTParser::test_full_nrt_parsing_integration": true}