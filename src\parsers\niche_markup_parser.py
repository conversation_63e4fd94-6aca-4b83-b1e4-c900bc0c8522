"""Niche markup parser for custom Niche formats."""

from typing import Dict, Any
import re
from bs4 import BeautifulSoup
from .base import BaseParser, ParsingError


class NicheMarkupParser(BaseParser):
    """Parser for Niche-specific markup formats (nrt, nxdx)."""
    
    @property
    def supported_types(self) -> list:
        """Return list of supported content types."""
        return ['niche_markup', 'nrt', 'nxdx']
    
    def parse(self, data: bytes, metadata: Dict[str, Any]) -> Dict[str, Any]:
        """Parse Niche markup content and extract text.
        
        Args:
            data: Raw Niche markup data
            metadata: Content metadata
            
        Returns:
            Dictionary with parsed results
            
        Raises:
            ParsingError: If parsing fails
        """
        try:
            # Decode the content
            content = self.safe_decode(data)
            
            # Determine specific Niche format
            niche_format = self._detect_niche_format(content, metadata)
            
            # Parse based on detected format
            if niche_format == 'nrt':
                text = self._parse_nrt(content)
            elif niche_format == 'nxdx':
                text = self._parse_nxdx(content)
            else:
                # Generic Niche markup parsing
                text = self._parse_generic_niche(content)
            
            # Normalize the extracted text
            normalized_text = self.normalize_text(text)
            
            parsing_metadata = {
                'niche_format': niche_format,
                'encoding_used': self.detect_encoding(data),
                'original_length': len(data),
                'content_length': len(content),
                'extracted_length': len(text),
                'normalized_length': len(normalized_text)
            }
            
            return self.create_result(normalized_text, metadata, parsing_metadata)
            
        except Exception as e:
            raise ParsingError(f"Failed to parse Niche markup content: {e}")
    
    def _detect_niche_format(self, content: str, metadata: Dict[str, Any]) -> str:
        """Detect specific Niche format.
        
        Args:
            content: Decoded content string
            metadata: Content metadata
            
        Returns:
            Detected Niche format
        """
        # Check metadata first
        real_type = metadata.get('real_type', '').lower()
        if real_type in ['nrt', 'nxdx']:
            return real_type
        
        # Check content patterns
        content_lower = content.lower()
        
        if 'nrt' in content_lower or '<nrt' in content_lower:
            return 'nrt'
        elif 'nxdx' in content_lower or '<nxdx' in content_lower:
            return 'nxdx'
        else:
            return 'generic'
    
    def _parse_nrt(self, content: str) -> str:
        """Parse NRT (Niche Report Template) format.
        
        Args:
            content: NRT content string
            
        Returns:
            Extracted text
        """
        try:
            # NRT files are typically XML-based with custom tags
            # Remove common NRT-specific tags while preserving content
            
            # First, try to parse as XML
            soup = BeautifulSoup(content, 'xml')
            
            # Remove metadata and formatting tags but keep content
            for tag in soup.find_all(['meta', 'style', 'script', 'header', 'footer']):
                tag.decompose()
            
            # Extract text from remaining content
            text = soup.get_text()
            
            # Clean up NRT-specific patterns
            text = self._clean_nrt_patterns(text)
            
            return text
            
        except Exception:
            # Fallback to regex-based cleaning
            return self._parse_with_regex(content, 'nrt')
    
    def _parse_nxdx(self, content: str) -> str:
        """Parse NXDX format.
        
        Args:
            content: NXDX content string
            
        Returns:
            Extracted text
        """
        try:
            # NXDX files may have different structure
            # Try XML parsing first
            soup = BeautifulSoup(content, 'xml')
            
            # Look for specific NXDX content containers
            content_tags = soup.find_all(['content', 'text', 'body', 'data'])
            
            if content_tags:
                text_parts = []
                for tag in content_tags:
                    text_parts.append(tag.get_text())
                text = '\n'.join(text_parts)
            else:
                # Extract all text if no specific containers found
                text = soup.get_text()
            
            # Clean up NXDX-specific patterns
            text = self._clean_nxdx_patterns(text)
            
            return text
            
        except Exception:
            # Fallback to regex-based cleaning
            return self._parse_with_regex(content, 'nxdx')
    
    def _parse_generic_niche(self, content: str) -> str:
        """Parse generic Niche markup.
        
        Args:
            content: Generic Niche content string
            
        Returns:
            Extracted text
        """
        # Try multiple approaches
        
        # 1. Try as XML/HTML
        try:
            soup = BeautifulSoup(content, 'html.parser')
            text = soup.get_text()
            if text.strip():
                return self._clean_generic_niche_patterns(text)
        except Exception:
            pass
        
        # 2. Try regex-based cleaning
        try:
            return self._parse_with_regex(content, 'generic')
        except Exception:
            pass
        
        # 3. Last resort: return content with minimal cleaning
        return self._minimal_clean(content)
    
    def _clean_nrt_patterns(self, text: str) -> str:
        """Clean NRT-specific patterns from text.
        
        Args:
            text: Raw extracted text
            
        Returns:
            Cleaned text
        """
        # Remove common NRT metadata patterns
        patterns = [
            r'NRT_VERSION:\s*[\d.]+',
            r'TEMPLATE_ID:\s*\w+',
            r'CREATED_DATE:\s*[\d/\-\s:]+',
            r'MODIFIED_DATE:\s*[\d/\-\s:]+',
            r'</?nrt[^>]*>',
            r'</?template[^>]*>',
            r'</?field[^>]*>',
        ]
        
        for pattern in patterns:
            text = re.sub(pattern, '', text, flags=re.IGNORECASE)
        
        return text
    
    def _clean_nxdx_patterns(self, text: str) -> str:
        """Clean NXDX-specific patterns from text.
        
        Args:
            text: Raw extracted text
            
        Returns:
            Cleaned text
        """
        # Remove common NXDX metadata patterns
        patterns = [
            r'NXDX_VERSION:\s*[\d.]+',
            r'DOCUMENT_ID:\s*\w+',
            r'</?nxdx[^>]*>',
            r'</?document[^>]*>',
            r'</?section[^>]*>',
        ]
        
        for pattern in patterns:
            text = re.sub(pattern, '', text, flags=re.IGNORECASE)
        
        return text
    
    def _clean_generic_niche_patterns(self, text: str) -> str:
        """Clean generic Niche patterns from text.
        
        Args:
            text: Raw extracted text
            
        Returns:
            Cleaned text
        """
        # Remove common Niche system patterns
        patterns = [
            r'NICHE_ID:\s*\w+',
            r'SYSTEM_GENERATED:\s*[\w\s]+',
            r'</?niche[^>]*>',
            r'</?system[^>]*>',
        ]
        
        for pattern in patterns:
            text = re.sub(pattern, '', text, flags=re.IGNORECASE)
        
        return text
    
    def _parse_with_regex(self, content: str, format_type: str) -> str:
        """Parse content using regex patterns when XML parsing fails.
        
        Args:
            content: Content string
            format_type: Format type for specific patterns
            
        Returns:
            Extracted text
        """
        # Remove XML/HTML tags
        text = re.sub(r'<[^>]+>', '', content)
        
        # Remove common markup patterns
        text = re.sub(r'&\w+;', ' ', text)  # HTML entities
        text = re.sub(r'\{\{[^}]+\}\}', '', text)  # Template variables
        text = re.sub(r'\[\[[^\]]+\]\]', '', text)  # Wiki-style links
        
        # Apply format-specific cleaning
        if format_type == 'nrt':
            text = self._clean_nrt_patterns(text)
        elif format_type == 'nxdx':
            text = self._clean_nxdx_patterns(text)
        else:
            text = self._clean_generic_niche_patterns(text)
        
        return text
    
    def _minimal_clean(self, content: str) -> str:
        """Minimal cleaning for content that can't be parsed otherwise.
        
        Args:
            content: Content string
            
        Returns:
            Minimally cleaned text
        """
        # Just remove obvious markup and normalize whitespace
        text = re.sub(r'<[^>]+>', ' ', content)
        text = re.sub(r'&\w+;', ' ', text)
        text = re.sub(r'\s+', ' ', text)
        return text.strip()
