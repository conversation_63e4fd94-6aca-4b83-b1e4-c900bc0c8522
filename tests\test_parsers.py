"""Tests for parser modules."""

import pytest
from src.parsers.text_parser import <PERSON><PERSON>ars<PERSON>
from src.parsers.markup_parser import MarkupParser
from src.parsers.factory import ParserFactory
from src.parsers.base import ParsingError


class TestTextParser:
    """Test cases for TextParser."""
    
    def setup_method(self):
        """Setup test fixtures."""
        self.parser = TextParser()
    
    def test_parse_utf8_text(self):
        """Test parsing UTF-8 text."""
        test_data = "Hello, world! This is a test.".encode('utf-8')
        metadata = {'category': 'text'}
        
        result = self.parser.parse(test_data, metadata)
        
        assert result['parsing_successful'] is True
        assert result['normalized_text'] == "Hello, world! This is a test."
        assert result['text_length'] == len("Hello, world! This is a test.")
        assert result['parser_type'] == 'TextParser'
    
    def test_parse_text_with_whitespace(self):
        """Test parsing text with excessive whitespace."""
        test_data = "  Hello,   world!  \n\n\n  This   is   a   test.  \n\n  ".encode('utf-8')
        metadata = {'category': 'text'}
        
        result = self.parser.parse(test_data, metadata)
        
        assert result['parsing_successful'] is True
        # Should normalize whitespace
        assert "Hello, world!" in result['normalized_text']
        assert "This is a test." in result['normalized_text']
        # Should not have excessive whitespace
        assert "   " not in result['normalized_text']
    
    def test_supported_types(self):
        """Test supported types property."""
        assert 'text' in self.parser.supported_types
        assert 'txt' in self.parser.supported_types


class TestMarkupParser:
    """Test cases for MarkupParser."""
    
    def setup_method(self):
        """Setup test fixtures."""
        self.parser = MarkupParser()
    
    def test_parse_html(self):
        """Test parsing HTML content."""
        html_data = """
        <html>
            <head><title>Test Page</title></head>
            <body>
                <h1>Hello World</h1>
                <p>This is a test paragraph.</p>
                <script>alert('test');</script>
            </body>
        </html>
        """.encode('utf-8')
        metadata = {'category': 'markup'}
        
        result = self.parser.parse(html_data, metadata)
        
        assert result['parsing_successful'] is True
        assert "Hello World" in result['normalized_text']
        assert "This is a test paragraph." in result['normalized_text']
        # Script content should be removed
        assert "alert" not in result['normalized_text']
    
    def test_parse_xml(self):
        """Test parsing XML content."""
        xml_data = """
        <?xml version="1.0"?>
        <document>
            <title>Test Document</title>
            <content>This is XML content.</content>
        </document>
        """.encode('utf-8')
        metadata = {'category': 'markup'}
        
        result = self.parser.parse(xml_data, metadata)
        
        assert result['parsing_successful'] is True
        assert "Test Document" in result['normalized_text']
        assert "This is XML content." in result['normalized_text']
    
    def test_parse_json(self):
        """Test parsing JSON content."""
        json_data = """
        {
            "title": "Test Document",
            "content": "This is JSON content.",
            "metadata": {
                "author": "Test Author",
                "date": "2024-01-01"
            }
        }
        """.encode('utf-8')
        metadata = {'category': 'markup'}
        
        result = self.parser.parse(json_data, metadata)
        
        assert result['parsing_successful'] is True
        assert "Test Document" in result['normalized_text']
        assert "This is JSON content." in result['normalized_text']
        assert "Test Author" in result['normalized_text']
    
    def test_detect_markup_type(self):
        """Test markup type detection."""
        # Test HTML detection
        html_content = "<!DOCTYPE html><html><body>Test</body></html>"
        assert self.parser._detect_markup_type(html_content) == 'html'
        
        # Test XML detection
        xml_content = "<?xml version='1.0'?><root>Test</root>"
        assert self.parser._detect_markup_type(xml_content) == 'xml'
        
        # Test JSON detection
        json_content = '{"key": "value"}'
        assert self.parser._detect_markup_type(json_content) == 'json'
    
    def test_supported_types(self):
        """Test supported types property."""
        supported = self.parser.supported_types
        assert 'markup' in supported
        assert 'html' in supported
        assert 'xml' in supported
        assert 'json' in supported


class TestParserFactory:
    """Test cases for ParserFactory."""
    
    def setup_method(self):
        """Setup test fixtures."""
        self.factory = ParserFactory()
    
    def test_get_parser_for_supported_category(self):
        """Test getting parser for supported category."""
        parser = self.factory.get_parser('text')
        assert parser is not None
        assert isinstance(parser, TextParser)
        
        parser = self.factory.get_parser('markup')
        assert parser is not None
        assert isinstance(parser, MarkupParser)
    
    def test_get_parser_for_unsupported_category(self):
        """Test getting parser for unsupported category."""
        parser = self.factory.get_parser('unsupported_type')
        assert parser is None
    
    def test_parse_content_success(self):
        """Test successful content parsing."""
        test_data = "Hello, world!".encode('utf-8')
        metadata = {'category': 'text'}
        
        result = self.factory.parse_content(test_data, metadata)
        
        assert result['parsing_successful'] is True
        assert result['normalized_text'] == "Hello, world!"
    
    def test_parse_content_no_category(self):
        """Test parsing content without category."""
        test_data = "Hello, world!".encode('utf-8')
        metadata = {}
        
        with pytest.raises(ParsingError) as exc_info:
            self.factory.parse_content(test_data, metadata)
        
        assert "No category specified" in str(exc_info.value)
    
    def test_parse_content_unsupported_category(self):
        """Test parsing content with unsupported category."""
        test_data = "Hello, world!".encode('utf-8')
        metadata = {'category': 'unsupported_type'}
        
        with pytest.raises(ParsingError) as exc_info:
            self.factory.parse_content(test_data, metadata)
        
        assert "No parser available" in str(exc_info.value)
    
    def test_get_supported_categories(self):
        """Test getting list of supported categories."""
        categories = self.factory.get_supported_categories()
        assert 'text' in categories
        assert 'markup' in categories
        assert len(categories) > 0
    
    def test_is_category_supported(self):
        """Test checking if category is supported."""
        assert self.factory.is_category_supported('text') is True
        assert self.factory.is_category_supported('markup') is True
        assert self.factory.is_category_supported('unsupported') is False
    
    def test_get_parser_info(self):
        """Test getting parser information."""
        info = self.factory.get_parser_info()
        
        assert 'total_parsers' in info
        assert 'supported_categories' in info
        assert 'parser_details' in info
        assert info['total_parsers'] > 0
        assert len(info['supported_categories']) > 0
