"""Configuration management for Niche Text ETL."""

import os
import yaml
from typing import Dict, Any, List
from pydantic import BaseModel, Field
from dotenv import load_dotenv


class DatabaseConfig(BaseModel):
    """Database connection configuration."""
    driver: str
    server: str
    database: str
    username: str = ""
    password: str = ""
    trusted_connection: bool = True
    connection_timeout: int = 30
    command_timeout: int = 300


class ProcessingConfig(BaseModel):
    """Processing configuration."""
    batch_size: int = 1000
    max_workers: int = 4
    max_retries: int = 3
    retry_delay: int = 5
    memory_limit_mb: int = 2048
    categories_to_process: List[str] = Field(default_factory=list)
    categories_to_ignore: List[str] = Field(default_factory=list)


class LoggingConfig(BaseModel):
    """Logging configuration."""
    level: str = "INFO"
    log_dir: str = "logs"
    log_file: str = "niche_etl.log"
    max_file_size_mb: int = 100
    backup_count: int = 10
    retention_days: int = 30
    format: str = "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}"


class EmailConfig(BaseModel):
    """Email configuration."""
    smtp_server: str
    smtp_port: int = 587
    use_tls: bool = True
    sender: str
    recipients: List[str] = Field(default_factory=list)
    alert_schedule: str = "08:00"
    alert_timezone: str = "America/Toronto"
    subject_template: str = "Niche ETL Daily Report - {date}"
    error_threshold: int = 1


class PipelineConfig(BaseModel):
    """Pipeline configuration."""
    incremental_mode: bool = True
    checkpoint_frequency: int = 100
    source_table: str = "TBL_BlobData"
    destination_table: str = "ProcessedDocuments"
    checkpoint_table: str = "ETL_Checkpoints"
    host_id_prefixes: List[str] = Field(default_factory=list)


class MonitoringConfig(BaseModel):
    """Monitoring configuration."""
    health_check_interval: int = 300
    metrics_retention_days: int = 7
    performance_log_frequency: int = 1000


class Config(BaseModel):
    """Main configuration class."""
    database: Dict[str, DatabaseConfig]
    processing: ProcessingConfig
    logging: LoggingConfig
    email: EmailConfig
    pipeline: PipelineConfig
    monitoring: MonitoringConfig


def load_config(config_path: str = "config.yaml") -> Config:
    """Load configuration from YAML file with environment variable substitution."""
    # Load environment variables
    load_dotenv()
    
    # Read YAML file
    with open(config_path, 'r') as file:
        config_data = yaml.safe_load(file)
    
    # Substitute environment variables
    config_data = _substitute_env_vars(config_data)
    
    # Parse database configs
    db_configs = {}
    for db_name, db_config in config_data['database'].items():
        db_configs[db_name] = DatabaseConfig(**db_config)
    
    # Create main config
    return Config(
        database=db_configs,
        processing=ProcessingConfig(**config_data['processing']),
        logging=LoggingConfig(**config_data['logging']),
        email=EmailConfig(**config_data['email']),
        pipeline=PipelineConfig(**config_data['pipeline']),
        monitoring=MonitoringConfig(**config_data['monitoring'])
    )


def _substitute_env_vars(data: Any) -> Any:
    """Recursively substitute environment variables in configuration data."""
    if isinstance(data, dict):
        return {key: _substitute_env_vars(value) for key, value in data.items()}
    elif isinstance(data, list):
        return [_substitute_env_vars(item) for item in data]
    elif isinstance(data, str) and data.startswith("${") and data.endswith("}"):
        env_var = data[2:-1]
        return os.getenv(env_var, data)
    else:
        return data
