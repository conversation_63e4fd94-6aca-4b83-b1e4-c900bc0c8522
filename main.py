#!/usr/bin/env python3
"""Main entry point for Niche Text ETL."""

import sys
import click
import json
from pathlib import Path
from loguru import logger

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.config import load_config
from src.logging_setup import setup_logging
from src.pipeline import NicheTextPipeline
from src.monitoring import Monitor


@click.group()
@click.option('--config', '-c', default='config.yaml', help='Configuration file path')
@click.option('--log-level', '-l', help='Override log level')
@click.pass_context
def cli(ctx, config, log_level):
    """Niche Text ETL - Extract and normalize text from Niche database."""
    try:
        # Load configuration
        ctx.ensure_object(dict)
        ctx.obj['config'] = load_config(config)
        
        # Override log level if specified
        if log_level:
            ctx.obj['config'].logging.level = log_level.upper()
        
        # Setup logging
        setup_logging(ctx.obj['config'].logging)
        
        logger.info(f"Niche Text ETL starting with config: {config}")
        
    except Exception as e:
        click.echo(f"Error initializing application: {e}", err=True)
        sys.exit(1)


@cli.command()
@click.option('--limit', '-n', type=int, help='Limit number of records to process (for testing/debug)')
@click.pass_context
def run(ctx, limit):
    """Run the ETL pipeline."""
    try:
        config = ctx.obj['config']

        # Override debug limit if specified via CLI
        if limit:
            config.processing.max_records_to_process = limit
            logger.info(f"Debug mode: Limiting processing to {limit} records")

        pipeline = NicheTextPipeline(config)
        
        # Validate configuration
        validation = pipeline.validate_configuration()
        if not validation['valid']:
            logger.error("Configuration validation failed:")
            for error in validation['errors']:
                logger.error(f"  - {error}")
            sys.exit(1)
        
        if validation['warnings']:
            logger.warning("Configuration warnings:")
            for warning in validation['warnings']:
                logger.warning(f"  - {warning}")
        
        # Run pipeline
        logger.info("Starting ETL pipeline execution")
        stats = pipeline.run()
        
        # Log final statistics
        logger.info("Pipeline execution completed")
        logger.info(f"Final statistics: {json.dumps(stats, indent=2, default=str)}")
        
        # Exit with error code if there were failures
        if stats['records_failed'] > 0:
            logger.warning(f"Pipeline completed with {stats['records_failed']} failures")
            sys.exit(1)
        
    except KeyboardInterrupt:
        logger.info("Pipeline interrupted by user")
        sys.exit(130)
    except Exception as e:
        logger.error(f"Pipeline execution failed: {e}")
        sys.exit(1)


@cli.command()
@click.pass_context
def validate(ctx):
    """Validate configuration and dependencies."""
    try:
        config = ctx.obj['config']
        pipeline = NicheTextPipeline(config)
        
        click.echo("Validating configuration...")
        validation = pipeline.validate_configuration()
        
        if validation['valid']:
            click.echo("✓ Configuration is valid")
        else:
            click.echo("✗ Configuration validation failed:")
            for error in validation['errors']:
                click.echo(f"  - {error}")
        
        if validation['warnings']:
            click.echo("Warnings:")
            for warning in validation['warnings']:
                click.echo(f"  - {warning}")
        
        # Show parser information
        click.echo("\nAvailable parsers:")
        parser_info = pipeline.get_parser_info()
        for parser_name, details in parser_info['parser_details'].items():
            click.echo(f"  - {parser_name}: {details['supported_types']}")
        
        sys.exit(0 if validation['valid'] else 1)
        
    except Exception as e:
        click.echo(f"Validation failed: {e}", err=True)
        sys.exit(1)


@cli.command()
@click.pass_context
def monitor(ctx):
    """Run daily monitoring tasks."""
    try:
        config = ctx.obj['config']
        monitor = Monitor(config)
        
        logger.info("Running daily monitoring tasks")
        results = monitor.run_daily_monitoring()
        
        # Log results
        logger.info(f"Monitoring results: {json.dumps(results, indent=2, default=str)}")
        
        # Check if there were errors
        if 'error' in results:
            logger.error("Monitoring tasks failed")
            sys.exit(1)
        
        click.echo("Daily monitoring tasks completed successfully")
        
    except Exception as e:
        logger.error(f"Monitoring failed: {e}")
        sys.exit(1)


@cli.command()
@click.option('--hours', '-h', default=24, help='Hours to analyze (default: 24)')
@click.pass_context
def analyze_logs(ctx, hours):
    """Analyze log files for errors and warnings."""
    try:
        config = ctx.obj['config']
        monitor = Monitor(config)
        
        click.echo(f"Analyzing logs for the past {hours} hours...")
        analysis = monitor.log_analyzer.analyze_logs(hours_back=hours)
        
        # Display results
        click.echo(f"\nAnalysis Results:")
        click.echo(f"Period: {analysis['period_start']} to {analysis['period_end']}")
        click.echo(f"Total Errors: {analysis['total_errors']}")
        click.echo(f"Total Warnings: {analysis['total_warnings']}")
        click.echo(f"Files Analyzed: {len(analysis['files_analyzed'])}")
        
        if analysis['summary']['error_categories']:
            click.echo("\nError Categories:")
            for category, count in analysis['summary']['error_categories'].items():
                click.echo(f"  - {category}: {count}")
        
        if analysis['summary']['warning_categories']:
            click.echo("\nWarning Categories:")
            for category, count in analysis['summary']['warning_categories'].items():
                click.echo(f"  - {category}: {count}")
        
        # Show recent errors
        if analysis['errors']:
            click.echo(f"\nRecent Errors (last 5):")
            for error in analysis['errors'][-5:]:
                click.echo(f"  [{error['timestamp']}] {error['message']}")
        
    except Exception as e:
        click.echo(f"Log analysis failed: {e}", err=True)
        sys.exit(1)


@cli.command()
@click.pass_context
def info(ctx):
    """Show system information and configuration."""
    try:
        config = ctx.obj['config']
        
        click.echo("Niche Text ETL System Information")
        click.echo("=" * 40)
        
        # Configuration summary
        click.echo(f"Configuration file: {ctx.params.get('config', 'config.yaml')}")
        click.echo(f"Log level: {config.logging.level}")
        click.echo(f"Log directory: {config.logging.log_dir}")
        click.echo(f"Batch size: {config.processing.batch_size}")
        click.echo(f"Max workers: {config.processing.max_workers}")

        # Show debug limit if set
        if config.processing.max_records_to_process:
            click.echo(f"Debug limit: {config.processing.max_records_to_process} records")
        else:
            click.echo("Debug limit: None (unlimited processing)")
        
        # Database info
        click.echo(f"\nDatabase Configuration:")
        for db_name, db_config in config.database.items():
            click.echo(f"  {db_name}: {db_config.server}/{db_config.database}")
        
        # Processing categories
        click.echo(f"\nCategories to process: {config.processing.categories_to_process}")
        click.echo(f"Categories to ignore: {config.processing.categories_to_ignore}")
        
        # Email configuration
        click.echo(f"\nEmail alerts:")
        click.echo(f"  SMTP server: {config.email.smtp_server}:{config.email.smtp_port}")
        click.echo(f"  Sender: {config.email.sender}")
        click.echo(f"  Recipients: {config.email.recipients}")
        click.echo(f"  Alert schedule: {config.email.alert_schedule}")
        
    except Exception as e:
        click.echo(f"Failed to show system info: {e}", err=True)
        sys.exit(1)


@cli.command()
@click.option('--force', '-f', is_flag=True, help='Force cleanup without confirmation')
@click.pass_context
def cleanup(ctx, force):
    """Clean up old log files."""
    try:
        config = ctx.obj['config']
        monitor = Monitor(config)
        
        if not force:
            click.confirm(
                f"This will delete log files older than {config.logging.retention_days} days. Continue?",
                abort=True
            )
        
        click.echo("Cleaning up old log files...")
        results = monitor.log_rotator.rotate_logs()
        
        click.echo(f"Cleanup completed:")
        click.echo(f"  Files deleted: {results['total_deleted']}")
        click.echo(f"  Files kept: {results['total_kept']}")
        click.echo(f"  Space freed: {results['space_freed']} bytes")
        
    except Exception as e:
        click.echo(f"Cleanup failed: {e}", err=True)
        sys.exit(1)


if __name__ == '__main__':
    cli()
