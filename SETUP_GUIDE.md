# Quick Setup Guide

## ✅ System Status
Your Niche Text ETL system is **successfully installed and working**! 

The validation shows:
- ✅ Application loads correctly
- ✅ All 6 parsers are registered and working
- ✅ Logging system is functional
- ✅ Configuration system is working
- ❌ Database connections need to be configured (expected)

## Next Steps

### 1. Configure Database Connections

Edit `config.yaml` and update the database sections with your actual server details:

```yaml
database:
  source:
    driver: "ODBC Driver 17 for SQL Server"
    server: "YOUR_SOURCE_SERVER_NAME"        # Replace with actual server
    database: "YOUR_SOURCE_DATABASE_NAME"    # Replace with actual database
    trusted_connection: true                 # Use Windows Authentication
    # OR for SQL Authentication:
    # trusted_connection: false
    # username: "${DB_SOURCE_USER}"
    # password: "${DB_SOURCE_PASSWORD}"
  
  destination:
    driver: "ODBC Driver 17 for SQL Server"
    server: "YOUR_DEST_SERVER_NAME"          # Replace with actual server
    database: "YOUR_DEST_DATABASE_NAME"      # Replace with actual database
    trusted_connection: true                 # Use Windows Authentication
```

### 2. Set Up Destination Database

Run the SQL script to create the required tables:

```sql
-- Connect to your destination database and run:
sqlcmd -S YOUR_DEST_SERVER -d YOUR_DEST_DATABASE -i sql/create_destination_tables.sql
```

Or manually execute the SQL script in SQL Server Management Studio.

### 3. Configure Email Alerts (Optional)

Update the email section in `config.yaml`:

```yaml
email:
  smtp_server: "YOUR_SMTP_SERVER"           # e.g., "smtp.haltonpolice.ca"
  smtp_port: 587
  sender: "<EMAIL>"
  recipients:
    - "<EMAIL>"
    - "<EMAIL>"          # Add your email
```

### 4. Test Configuration

After updating the configuration:

```bash
python main.py validate
```

You should see:
```
✓ Configuration is valid
```

### 5. Run Your First ETL

```bash
python main.py run
```

## Configuration Examples

### Example 1: Windows Authentication
```yaml
database:
  source:
    server: "SQLSERVER01"
    database: "NicheDatabase"
    trusted_connection: true
```

### Example 2: SQL Server Authentication
```yaml
database:
  source:
    server: "SQLSERVER01"
    database: "NicheDatabase"
    trusted_connection: false
    username: "${DB_SOURCE_USER}"
    password: "${DB_SOURCE_PASSWORD}"
```

Then create `.env` file:
```bash
DB_SOURCE_USER=your_username
DB_SOURCE_PASSWORD=your_password
```

### Example 3: Named Instance
```yaml
database:
  source:
    server: "SQLSERVER01\\INSTANCE1"
    database: "NicheDatabase"
    trusted_connection: true
```

### Example 4: IP Address with Port
```yaml
database:
  source:
    server: "*************,1433"
    database: "NicheDatabase"
    trusted_connection: true
```

## Troubleshooting Database Connections

### Common Issues:

1. **Named Pipes Provider Error**
   - Enable TCP/IP in SQL Server Configuration Manager
   - Restart SQL Server service
   - Check Windows Firewall settings

2. **Login Timeout**
   - Verify server name is correct
   - Check network connectivity: `telnet SERVER_NAME 1433`
   - Increase connection_timeout in config

3. **Authentication Failed**
   - For Windows Auth: Run as user with database access
   - For SQL Auth: Verify username/password in .env file

4. **Database Not Found**
   - Verify database name is correct
   - Check user has access to the database

### Test Database Connectivity

```bash
# Test with sqlcmd (if available)
sqlcmd -S YOUR_SERVER -d YOUR_DATABASE -E

# Test with Python
python -c "import pyodbc; print(pyodbc.drivers())"
```

## Performance Tuning

Once connected, you can adjust these settings in `config.yaml`:

```yaml
processing:
  batch_size: 1000      # Start with 1000, adjust based on performance
  max_workers: 4        # Number of CPU cores or less
  memory_limit_mb: 2048 # Adjust based on available RAM
```

## Monitoring Setup

The system will automatically:
- Create log files in `logs/` directory
- Send daily email reports at 8:00 AM (if configured)
- Rotate logs after 30 days

## Ready to Go!

Your system is properly installed. Just update the database configuration and you'll be ready to process your Niche data!

For any issues, check the logs:
```bash
tail -f logs/niche_etl.log
```
