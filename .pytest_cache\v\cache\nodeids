["tests/test_decompression.py::TestDecompressor::test_decompress_gzipped_data", "tests/test_decompression.py::TestDecompressor::test_decompress_invalid_gzip", "tests/test_decompression.py::TestDecompressor::test_decompress_raw_data", "tests/test_decompression.py::TestDecompressor::test_decompress_size_limit_exceeded", "tests/test_decompression.py::TestDecompressor::test_is_likely_compressed", "tests/test_decompression.py::TestDecompressor::test_validate_decompressed_content", "tests/test_nrt_parser.py::TestNRTParser::test_full_nrt_parsing_integration", "tests/test_nrt_parser.py::TestNRTParser::test_nrt_cleanup_artifacts", "tests/test_nrt_parser.py::TestNRTParser::test_nrt_formatting_removal", "tests/test_nrt_parser.py::TestNRTParser::test_nrt_link_removal", "tests/test_nrt_parser.py::TestNRTParser::test_nrt_paragraph_processing", "tests/test_nrt_parser.py::TestNRTParser::test_nrt_section_headers", "tests/test_nrt_parser.py::TestNRTParser::test_nrt_table_processing", "tests/test_nrt_parser.py::TestNRTParser::test_nrt_ui_instruction_removal", "tests/test_nrt_parser.py::TestNRTParser::test_parse_nrt_structure", "tests/test_parsers.py::TestMarkupParser::test_detect_markup_type", "tests/test_parsers.py::TestMarkupParser::test_parse_html", "tests/test_parsers.py::TestMarkupParser::test_parse_json", "tests/test_parsers.py::TestMarkupParser::test_parse_xml", "tests/test_parsers.py::TestMarkupParser::test_supported_types", "tests/test_parsers.py::TestParserFactory::test_get_parser_for_supported_category", "tests/test_parsers.py::TestParserFactory::test_get_parser_for_unsupported_category", "tests/test_parsers.py::TestParserFactory::test_get_parser_info", "tests/test_parsers.py::TestParserFactory::test_get_supported_categories", "tests/test_parsers.py::TestParserFactory::test_is_category_supported", "tests/test_parsers.py::TestParserFactory::test_parse_content_no_category", "tests/test_parsers.py::TestParserFactory::test_parse_content_success", "tests/test_parsers.py::TestParserFactory::test_parse_content_unsupported_category", "tests/test_parsers.py::TestTextParser::test_parse_text_with_whitespace", "tests/test_parsers.py::TestTextParser::test_parse_utf8_text", "tests/test_parsers.py::TestTextParser::test_supported_types"]