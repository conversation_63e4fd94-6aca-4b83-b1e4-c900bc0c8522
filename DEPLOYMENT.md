# Deployment Guide

This guide covers deployment options for the Niche Text ETL system.

## Prerequisites

### System Requirements

- **Operating System**: Windows Server 2016+ or Linux
- **Python**: 3.8 or higher
- **Memory**: Minimum 4GB RAM, recommended 8GB+
- **Storage**: 10GB+ free space for logs and temporary files
- **Network**: Access to source and destination SQL Server instances

### Database Requirements

- **Source Database**: SQL Server 2012+ with read access
- **Destination Database**: SQL Server 2012+ with read/write access
- **ODBC Driver**: Microsoft ODBC Driver 17 for SQL Server

## Installation Methods

### Method 1: Direct Python Installation

1. **Install Python Dependencies**
   ```bash
   pip install -r requirements.txt
   ```

2. **Setup Database Schema**
   ```sql
   sqlcmd -S destination-server -d destination-db -i sql/create_destination_tables.sql
   ```

3. **Configure Application**
   ```bash
   cp config.yaml.example config.yaml
   cp .env.example .env
   # Edit configuration files
   ```

4. **Validate Setup**
   ```bash
   python main.py validate
   ```

### Method 2: Docker Deployment

1. **Build Docker Image**
   ```bash
   docker build -t niche-text-etl:latest .
   ```

2. **Create Environment File**
   ```bash
   cp .env.example .env
   # Edit with your credentials
   ```

3. **Deploy with Docker Compose**
   ```bash
   docker-compose up -d
   ```

4. **Verify Deployment**
   ```bash
   docker-compose logs -f
   ```

## Configuration

### Database Configuration

Update `config.yaml` with your database settings:

```yaml
database:
  source:
    server: "your-source-server.domain.com"
    database: "your_source_database"
    trusted_connection: true  # or false if using SQL auth
  destination:
    server: "your-dest-server.domain.com"
    database: "your_dest_database"
    trusted_connection: true
```

### Environment Variables

Create `.env` file with sensitive information:

```bash
# Database credentials (if not using integrated auth)
DB_SOURCE_USER=your_source_username
DB_SOURCE_PASSWORD=your_source_password
DB_DEST_USER=your_dest_username
DB_DEST_PASSWORD=your_dest_password

# Email credentials (if SMTP requires auth)
EMAIL_USERNAME=your_email_username
EMAIL_PASSWORD=your_email_password
```

### Performance Tuning

Adjust these settings based on your environment:

```yaml
processing:
  batch_size: 1000        # Records per batch
  max_workers: 4          # Parallel processing threads
  memory_limit_mb: 2048   # Max memory per file
```

## Scheduling

### Windows Task Scheduler

1. **Create Basic Task**
   - Open Task Scheduler
   - Create Basic Task
   - Name: "Niche Text ETL"

2. **Configure Trigger**
   - Frequency: Daily
   - Time: Choose appropriate time (e.g., 2:00 AM)

3. **Configure Action**
   - Program: `python`
   - Arguments: `main.py run`
   - Start in: `C:\path\to\niche_text_etl`

4. **Configure Conditions**
   - Uncheck "Start only if computer is on AC power"
   - Check "Wake computer to run this task"

### Linux Cron

Add to crontab:

```bash
# Run ETL daily at 2:00 AM
0 2 * * * cd /path/to/niche_text_etl && python main.py run

# Run monitoring daily at 8:00 AM
0 8 * * * cd /path/to/niche_text_etl && python main.py monitor
```

### Docker Cron

For containerized deployment, use a cron container or host-level scheduling:

```yaml
# docker-compose.yml addition
  cron:
    image: niche-text-etl:latest
    command: crond -f
    volumes:
      - ./crontab:/etc/crontabs/root
    depends_on:
      - niche-etl
```

## Monitoring Setup

### Log Configuration

Ensure log directory is writable:

```bash
mkdir -p logs
chmod 755 logs
```

### Email Alerts

Configure SMTP settings in `config.yaml`:

```yaml
email:
  smtp_server: "smtp.company.com"
  smtp_port: 587
  use_tls: true
  sender: "<EMAIL>"
  recipients:
    - "<EMAIL>"
    - "<EMAIL>"
```

### Health Checks

Create a health check script:

```bash
#!/bin/bash
# health_check.sh
cd /path/to/niche_text_etl
python main.py validate
if [ $? -eq 0 ]; then
    echo "ETL system healthy"
    exit 0
else
    echo "ETL system unhealthy"
    exit 1
fi
```

## Security Considerations

### Database Security

1. **Use Integrated Authentication** when possible
2. **Create dedicated service accounts** with minimal permissions
3. **Encrypt connections** using SSL/TLS
4. **Regular password rotation** for SQL authentication

### File System Security

1. **Restrict file permissions** on configuration files
2. **Secure log directory** access
3. **Regular cleanup** of temporary files

### Network Security

1. **Firewall rules** for database connections
2. **VPN or private networks** for sensitive data
3. **SMTP security** for email alerts

## Backup and Recovery

### Configuration Backup

```bash
# Backup configuration
tar -czf etl-config-backup-$(date +%Y%m%d).tar.gz config.yaml .env

# Backup logs (optional)
tar -czf etl-logs-backup-$(date +%Y%m%d).tar.gz logs/
```

### Database Backup

Ensure regular backups of:
- Destination database (processed data)
- ETL checkpoint table (processing state)

### Recovery Procedures

1. **Configuration Recovery**
   ```bash
   tar -xzf etl-config-backup-YYYYMMDD.tar.gz
   ```

2. **Reset Processing State**
   ```sql
   UPDATE ETL_Checkpoints 
   SET LastProcessedID = 0 
   WHERE ProcessName = 'niche_text_etl';
   ```

3. **Partial Recovery**
   ```sql
   UPDATE ETL_Checkpoints 
   SET LastProcessedID = [specific_id] 
   WHERE ProcessName = 'niche_text_etl';
   ```

## Troubleshooting

### Common Deployment Issues

1. **Database Connection Failures**
   ```bash
   # Test connectivity
   python main.py validate
   
   # Check ODBC drivers
   odbcinst -q -d
   ```

2. **Permission Issues**
   ```bash
   # Check file permissions
   ls -la config.yaml .env
   
   # Fix permissions
   chmod 600 config.yaml .env
   ```

3. **Memory Issues**
   ```bash
   # Monitor memory usage
   python main.py run --log-level DEBUG
   
   # Reduce batch size in config.yaml
   ```

### Performance Issues

1. **Slow Processing**
   - Increase `max_workers` (up to CPU cores)
   - Optimize database indexes
   - Check network latency

2. **High Memory Usage**
   - Reduce `batch_size`
   - Lower `memory_limit_mb`
   - Monitor large file processing

3. **Database Timeouts**
   - Increase `command_timeout` in config
   - Check database server performance
   - Optimize source queries

## Maintenance

### Regular Tasks

1. **Weekly**
   - Review error logs
   - Check processing statistics
   - Verify email alerts

2. **Monthly**
   - Update dependencies
   - Review performance metrics
   - Clean up old logs

3. **Quarterly**
   - Security review
   - Configuration audit
   - Disaster recovery testing

### Updates and Patches

1. **Backup current installation**
2. **Test in staging environment**
3. **Update dependencies**
   ```bash
   pip install -r requirements.txt --upgrade
   ```
4. **Validate configuration**
   ```bash
   python main.py validate
   ```
5. **Deploy to production**

## Support

For deployment issues:

1. Check logs: `tail -f logs/niche_etl.log`
2. Run validation: `python main.py validate`
3. Review configuration files
4. Contact system administrator
